/**
 * Unified Database Client for StayOnBeat
 * Automatically selects between Neon, Local, or Supabase based on environment
 */

import { localDatabase, shouldUseLocalDatabase } from './local-database'
import { createClient } from "@supabase/supabase-js"

// Only import Neon on server side
let neonClient: any = null
let testNeonConnection: any = null

if (typeof window === 'undefined') {
  // Server-side only imports
  try {
    const neonModule = require('./neon')
    neonClient = neonModule.neonClient
    testNeonConnection = neonModule.testNeonConnection
  } catch (error) {
    console.warn('Neon client not available:', error)
  }
}

// Supabase fallback configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

let supabaseClient: any = null

// Create Supabase client only if needed as fallback
function createSupabaseClient() {
  if (!supabaseClient && supabaseUrl && supabaseAnonKey) {
    supabaseClient = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
      },
    })
  }
  return supabaseClient
}

/**
 * Get the appropriate database client based on environment and availability
 * Priority: Neon > Local > Supabase (fallback)
 */
export function getDatabaseClient() {
  // Priority 1: Neon Database (production) - server side only
  if (process.env.DATABASE_URL && neonClient && typeof window === 'undefined') {
    console.log('🚀 Using Neon PostgreSQL database')
    return neonClient
  }

  // Priority 2: Local Database (development)
  if (shouldUseLocalDatabase()) {
    console.log('📁 Using local database')
    return localDatabase
  }

  // Priority 3: Supabase (fallback only)
  console.log('☁️ Using Supabase database (fallback)')
  return createSupabaseClient()
}

// Export the unified client
export const client = getDatabaseClient()
export const database = client

// Legacy exports for compatibility
export const supabase = client

// Helper function to get the current user (works with Clerk + any database)
export async function getCurrentUser() {
  try {
    // If using Supabase fallback, get user from Supabase auth
    if (!process.env.DATABASE_URL && !shouldUseLocalDatabase()) {
      const supabase = createSupabaseClient()
      if (supabase) {
        const { data: { session } } = await supabase.auth.getSession()
        return session?.user
      }
    }

    // For Neon/Local, user management is handled by Clerk
    // Return null as user info comes from Clerk context
    return null
  } catch (error) {
    console.error("Error getting current user:", error)
    return null
  }
}

// Test database connection
export async function testDatabaseConnection() {
  if (process.env.DATABASE_URL && testNeonConnection && typeof window === 'undefined') {
    return await testNeonConnection()
  } else if (shouldUseLocalDatabase()) {
    console.log('📁 Local database is always available')
    return true
  } else {
    try {
      const supabase = createSupabaseClient()
      if (supabase) {
        const { data, error } = await supabase.from('submissions').select('count').limit(1)
        return !error
      }
      return false
    } catch (error) {
      console.error('❌ Supabase connection test failed:', error)
      return false
    }
  }
}

// Export database type for components to know which features are available
export function getDatabaseType(): 'neon' | 'local' | 'supabase' {
  if (process.env.DATABASE_URL && typeof window === 'undefined') return 'neon'
  if (shouldUseLocalDatabase()) return 'local'
  return 'supabase'
}

// Check if realtime features are available
export function hasRealtimeSupport(): boolean {
  const dbType = getDatabaseType()
  return dbType === 'supabase' // Only Supabase has realtime for now
}

console.log(`🔧 Database client initialized: ${getDatabaseType().toUpperCase()}`)
