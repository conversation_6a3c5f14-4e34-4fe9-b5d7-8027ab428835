"use client"

import { useState, useEffect } from "react"
import { useSupabasePlayer } from "@/hooks/use-supabase-player"
import { AdminPlayer } from "@/components/video-player/admin-player"
import { PublicPlayer } from "@/components/video-player/public-player"
import { Skeleton } from "@/components/ui/skeleton"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertCircle, RefreshCw } from "lucide-react"
import { Button } from "@/components/ui/button"
import { downloadM3U8FromSupabase } from "@/lib/m3u8-generator"

interface DatabasePlaylistPlayerProps {
  playlistId: string
  isAdmin?: boolean
  showControls?: boolean
  showLogo?: boolean
  className?: string
}

export function DatabasePlaylistPlayer({
  playlistId,
  isAdmin = false,
  showControls = true,
  showLogo = true,
  className = "",
}: DatabasePlaylistPlayerProps) {
  const { playlist, currentIndex, currentTrack, isLoading, error, handleTrackChange, refreshPlaylist } =
    useSupabasePlayer(playlistId)

  const [trackInfo, setTrackInfo] = useState<string>("")

  // Update track info when current track changes
  useEffect(() => {
    if (currentTrack) {
      setTrackInfo(`${currentTrack.songTitle} - ${currentTrack.artistName}`)
    } else {
      setTrackInfo("")
    }
  }, [currentTrack])

  // Handle download playlist
  const handleDownload = () => {
    if (playlistId) {
      downloadM3U8FromSupabase(playlistId)
    }
  }

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <Skeleton className="h-[300px] w-full rounded-md" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-2/3" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive" className={className}>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error}
          {refreshPlaylist && (
            <Button variant="outline" size="sm" onClick={refreshPlaylist} className="ml-2">
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          )}
        </AlertDescription>
      </Alert>
    )
  }

  if (playlist.length === 0) {
    return (
      <Alert className={className}>
        <AlertDescription>This playlist is empty or no tracks were found.</AlertDescription>
      </Alert>
    )
  }

  return (
    <div className={className}>
      {isAdmin ? (
        <AdminPlayer
          initialPlaylist={playlist}
          showLogo={showLogo}
          onTrackChange={(track) => {
            setTrackInfo(`${track.songTitle} - ${track.artistName}`)
          }}
        />
      ) : (
        <PublicPlayer
          initialPlaylist={playlist}
          showLogo={showLogo}
          onTrackChange={(track) => {
            setTrackInfo(`${track.songTitle} - ${track.artistName}`)
          }}
        />
      )}

      <div className="mt-4 flex justify-between items-center">
        <div className="text-sm text-gray-500">
          {trackInfo && <p className="font-medium">Now Playing: {trackInfo}</p>}
          <p>{playlist.length} tracks in playlist</p>
        </div>

        <Button variant="outline" size="sm" onClick={handleDownload} className="text-xs">
          Download M3U8
        </Button>
      </div>
    </div>
  )
}

// Keep the legacy export for backward compatibility
export { DatabasePlaylistPlayer as SupabasePlaylistPlayer }
export default DatabasePlaylistPlayer
